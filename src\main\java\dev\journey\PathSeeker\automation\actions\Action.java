package dev.journey.PathSeeker.automation.actions;

import dev.journey.PathSeeker.automation.AutomationFlow;

@FunctionalInterface
public interface Action {
    void execute(AutomationFlow flow);

    default Action then(Action next) {
        return flow -> {
            execute(flow);
            next.execute(flow);
        };
    }

    default Action onError(Action fallback) {
        return flow -> {
            try {
                execute(flow);
            } catch (Exception e) {
                fallback.execute(flow);
            }
        };
    }

    default Action withDelay(int ticks) {
        return flow -> {
            execute(flow);
            try {
                Thread.sleep(ticks * 50L); // Convert ticks to milliseconds
            } catch (InterruptedException ignored) {}
        };
    }

    default Action withRetry(int maxAttempts) {
        return flow -> {
            Exception lastException = null;
            for (int i = 0; i < maxAttempts; i++) {
                try {
                    execute(flow);
                    return;
                } catch (Exception e) {
                    lastException = e;
                    try {
                        Thread.sleep(1000); // Wait 1 second between retries
                    } catch (InterruptedException ignored) {}
                }
            }
            if (lastException != null) {
                throw new RuntimeException("Action failed after " + maxAttempts + " attempts", lastException);
            }
        };
    }
}