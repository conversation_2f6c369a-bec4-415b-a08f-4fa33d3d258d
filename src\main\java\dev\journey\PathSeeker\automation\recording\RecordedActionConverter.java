package dev.journey.PathSeeker.automation.recording;

import dev.journey.PathSeeker.automation.AutomationFlow;
import dev.journey.PathSeeker.automation.actions.Action;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.util.InputUtil;
import net.minecraft.client.util.InputUtil.Key;
import net.minecraft.client.network.ClientPlayerEntity;
import net.minecraft.client.option.KeyBinding;
import net.minecraft.text.Text;
import net.minecraft.util.math.Vec3d;

import java.util.ArrayList;
import java.util.List;

public class RecordedActionConverter {
    private static final MinecraftClient mc = MinecraftClient.getInstance();

    public static Action convertToAction(ActionRecorder.RecordedAction recordedAction) {
        return switch (recordedAction.type) {
            case KEYBOARD -> createKeyboardAction(recordedAction);
            case MOUSE -> createMouseAction(recordedAction);
            case CHAT -> createChatAction(recordedAction);
        };
    }

    public static List<Action> convertToActions(List<ActionRecorder.RecordedAction> recordedActions) {
        List<Action> actions = new ArrayList<>();
        for (int i = 0; i < recordedActions.size(); i++) {
            ActionRecorder.RecordedAction current = recordedActions.get(i);
            Action action = convertToAction(current);
            
            // Add delay between actions based on timestamps
            if (i < recordedActions.size() - 1) {
                long delay = recordedActions.get(i + 1).timestamp - current.timestamp;
                if (delay > 50) { // Only add delay if greater than 1 tick (50ms)
                    action = action.withDelay((int)(delay / 50)); // Convert ms to ticks
                }
            }
            
            actions.add(action);
        }
        return actions;
    }

    private static Action createKeyboardAction(ActionRecorder.RecordedAction recorded) {
        return flow -> {
            if (mc.player == null) return;
            
            // Log key binding details
            System.out.println("Converting key: " + recorded.key + " to InputUtil.Key");
            Key key = InputUtil.fromKeyCode(recorded.key, 0);
            System.out.println("Converted to key: " + key);
            
            // Simulate key press/release using InputUtil.Key
            KeyBinding.setKeyPressed(key, recorded.action != 0);
            
            // Move to recorded position if significant movement occurred
            if (!mc.player.getPos().equals(recorded.position)) {
                mc.player.setPosition(recorded.position.x, recorded.position.y, recorded.position.z);
            }
        };
    }

    private static Action createMouseAction(ActionRecorder.RecordedAction recorded) {
        return flow -> {
            if (mc.player == null) return;
            
            // Update position
            if (!mc.player.getPos().equals(recorded.position)) {
                mc.player.setPosition(recorded.position.x, recorded.position.y, recorded.position.z);
            }

            // Log mouse action details
            System.out.println("[Debug] Mouse Action Details:");
            System.out.println(" - Action value: " + recorded.action);
            System.out.println(" - Key/Button code: " + recorded.key);
            System.out.println(" - Position: " + recorded.position);

            // Simulate mouse click
            if (recorded.action == 1) { // Press
                ClientPlayerEntity player = mc.player;
                if (player != null) {
                    System.out.println(" - Executing mouse swing");
                    player.swingHand(player.getActiveHand());
                }
            }
            if (!mc.player.getPos().equals(recorded.position)) {
                mc.player.setPosition(recorded.position.x, recorded.position.y, recorded.position.z);
            }
        };
    }

    private static Action createChatAction(ActionRecorder.RecordedAction recorded) {
        return flow -> {
            if (mc.player == null) return;
            
            // Update position
            if (!mc.player.getPos().equals(recorded.position)) {
                mc.player.setPosition(recorded.position.x, recorded.position.y, recorded.position.z);
            }

            // Send chat message
            if (recorded.message != null && !recorded.message.isEmpty()) {
                mc.player.sendMessage(Text.literal(recorded.message), false);
            }
            if (!mc.player.getPos().equals(recorded.position)) {
                mc.player.setPosition(recorded.position.x, recorded.position.y, recorded.position.z);
            }
        };
    }
}