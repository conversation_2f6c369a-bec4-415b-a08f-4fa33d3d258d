package dev.journey.PathSeeker.modules.exploration;

import com.google.common.reflect.TypeToken;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import dev.journey.PathSeeker.PathSeeker;
import meteordevelopment.meteorclient.gui.GuiTheme;
import meteordevelopment.meteorclient.gui.WindowScreen;
import meteordevelopment.meteorclient.gui.widgets.*;
import meteordevelopment.meteorclient.gui.widgets.containers.WTable;
import meteordevelopment.meteorclient.gui.widgets.containers.WVerticalList;
import meteordevelopment.meteorclient.gui.widgets.input.*;
import meteordevelopment.meteorclient.gui.widgets.pressable.WButton;
import meteordevelopment.meteorclient.gui.widgets.pressable.WCheckbox;
import meteordevelopment.meteorclient.gui.widgets.pressable.WMinus;
import meteordevelopment.meteorclient.pathing.PathManagers;
import meteordevelopment.meteorclient.settings.*;
import meteordevelopment.meteorclient.systems.modules.Module;
import meteordevelopment.meteorclient.utils.Utils;
import meteordevelopment.meteorclient.utils.render.MeteorToast;
import net.fabricmc.loader.api.FabricLoader;
import net.minecraft.block.entity.*;
import net.minecraft.item.Items;
import net.minecraft.nbt.NbtCompound;
import net.minecraft.registry.RegistryKey;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.ChunkPos;
import net.minecraft.world.World;
import xaero.common.minimap.waypoints.Waypoint;
import xaero.hud.minimap.BuiltInHudModules;
import xaero.hud.minimap.module.MinimapSession;
import xaero.hud.minimap.waypoint.set.WaypointSet;
import xaero.hud.minimap.world.MinimapWorld;
import xaero.map.mods.SupportMods;
import xaeroplus.XaeroPlus;
import xaeroplus.event.ChunkDataEvent;
import xaeroplus.module.ModuleManager;
import xaeroplus.module.impl.OldChunks;
import xaeroplus.module.impl.PaletteNewChunks;

import java.io.*;
import java.util.*;
import java.util.stream.Collectors;
import java.util.function.Consumer;
import java.util.function.Predicate;

import static dev.journey.PathSeeker.utils.PathSeekerUtil.sendWebhook;

public class BetterStashFinder extends Module {
    public static final String MOD_ID = "Skylandia";
    public static final File FOLDER = FabricLoader.getInstance().getGameDir().resolve(MOD_ID).toFile();
    private static final Gson GSON = new GsonBuilder().setPrettyPrinting().create();

    private final SettingGroup sgGeneral = settings.getDefaultGroup();
    private final Setting<List<BlockEntityType<?>>> storageBlocks = sgGeneral.add(new StorageBlockListSetting.Builder()
            .name("storage-blocks")
            .description("Select the storage blocks to search for.")
            .defaultValue(StorageBlockListSetting.STORAGE_BLOCKS)
            .build()
    );
    private final Setting<Integer> minimumStorageCount = sgGeneral.add(new IntSetting.Builder()
            .name("minimum-storage-count")
            .description("The minimum amount of storage blocks in a chunk to record the chunk.")
            .defaultValue(4)
            .min(1)
            .sliderMin(1)
            .build()
    );
    private final Setting<Boolean> shulkerInstantHit = sgGeneral.add(new BoolSetting.Builder()
            .name("shulker-instant-hit")
            .description("If a single shulker counts as a stash.")
            .defaultValue(false)
            .build()
    );
    private final Setting<Integer> minimumDistance = sgGeneral.add(new IntSetting.Builder()
            .name("minimum-distance")
            .description("The minimum distance you must be from spawn to record a certain chunk.")
            .defaultValue(0)
            .min(0)
            .sliderMax(10000)
            .build()
    );
    private final Setting<Boolean> onlyOldchunks = sgGeneral.add(new BoolSetting.Builder()
            .name("only-old-chunks")
            .description("Checks that the chunks it scans have already been loaded.")
            .defaultValue(true)
            .build()
    );
    private final Setting<Boolean> sendNotifications = sgGeneral.add(new BoolSetting.Builder()
            .name("notifications")
            .description("Sends Minecraft notifications when new stashes are found.")
            .defaultValue(true)
            .build()
    );
    private final Setting<Mode> notificationMode = sgGeneral.add(new EnumSetting.Builder<Mode>()
            .name("notification-mode")
            .description("The mode to use for notifications.")
            .defaultValue(Mode.Both)
            .visible(sendNotifications::get)
            .build()
    );
    private final Setting<List<WebhookConfig>> webhooks = sgGeneral.add(new WebhookListSetting(
            "webhooks",
            "Configure multiple webhooks with different filters.",
            new ArrayList<>()
    ));

    public static class WebhookConfig {
        public String url = "";
        public String discordId = "";
        public boolean ping = false;
        public int minStorageCount = 0;
        public int maxStorageCount = Integer.MAX_VALUE;
        public boolean requireShulker = false;
        public boolean onlyOldChunks = false;
        public int minDistanceFromSpawn = 0;
        
        // Container type filters
        public boolean includeChests = true;
        public boolean includeBarrels = true;
        public boolean includeShulkers = true;
        public boolean includeEnderChests = true;
        public boolean includeHoppers = true;
        public boolean includeDispensersDroppers = true;
        public boolean includeFurnaces = true;
    }

    private class WebhookListSetting extends Setting<List<WebhookConfig>> {
        public WebhookListSetting(String name, String description, List<WebhookConfig> defaultValue) {
            super(name, description, defaultValue,
                null,  // onChanged
                null,  // onModuleActivated
                (IVisible) null  // visible
            );
        }

        @Override
        protected List<WebhookConfig> parseImpl(String str) {
            try {
                List<WebhookConfig> configs = GSON.fromJson(str, new TypeToken<List<WebhookConfig>>() {}.getType());
                return configs != null ? configs : new ArrayList<>(defaultValue);
            } catch (Exception e) {
                System.err.println("[PathSeeker] Error parsing webhook config: " + e.getMessage());
                return new ArrayList<>(defaultValue);
            }
        }

        @Override
        protected boolean isValueValid(List<WebhookConfig> value) {
            if (value == null) return false;
            for (WebhookConfig config : value) {
                if (config == null) return false;
            }
            return true;
        }

        @Override
        public List<WebhookConfig> load(NbtCompound tag) {
            try {
                String json = tag.getString(name);
                if (json.isEmpty()) return new ArrayList<>(defaultValue);
                
                List<WebhookConfig> configs = GSON.fromJson(json, new TypeToken<List<WebhookConfig>>() {}.getType());
                if (configs == null) return new ArrayList<>(defaultValue);
                
                // Validate each config
                configs.removeIf(config -> config == null);
                return configs;
            } catch (Exception e) {
                System.err.println("[PathSeeker] Error loading webhook configs: " + e.getMessage());
                return new ArrayList<>(defaultValue);
            }
        }

        @Override
        public NbtCompound save(NbtCompound tag) {
            try {
                List<WebhookConfig> configs = get();
                if (configs == null) configs = new ArrayList<>();
                
                // Remove any null configs before saving
                configs.removeIf(config -> config == null);
                
                String json = GSON.toJson(configs);
                tag.putString(name, json);
            } catch (Exception e) {
                System.err.println("[PathSeeker] Error saving webhook configs: " + e.getMessage());
            }
            return tag;
        }
    }
    public List<Chunk> chunks = new ArrayList<>();
    private final Setting<Boolean> saveToWaypoints = sgGeneral.add(new BoolSetting.Builder()
            .name("save-to-waypoints")
            .description("Creates xaeros minimap waypoints for stash finds.")
            .defaultValue(false)
            .onChanged(this::waypointSettingChanged)
            .build()
    );

    public BetterStashFinder() {
        super(PathSeeker.Hunting, "stash-finder", "Advanced stash finding functionality for Skylandia.");
    }

    private boolean isOldChunk(ChunkPos chunkPos, RegistryKey<World> dimension) {
        PaletteNewChunks paletteNewChunks = ModuleManager.getModule(PaletteNewChunks.class);
        boolean is119NewChunk = paletteNewChunks.isNewChunk(chunkPos.x, chunkPos.z, dimension);
        boolean is112OldChunk = ModuleManager.getModule(OldChunks.class).isOldChunk(chunkPos.x, chunkPos.z, dimension);
        return !is119NewChunk || is112OldChunk;
    }

    private void logWebhookDebug(WebhookConfig webhook, Chunk chunk, double distanceFromSpawn) {
        info("Webhook Debug for URL: " + webhook.url.substring(0, Math.min(webhook.url.length(), 20)) + "...");
        info("- Total Storage: " + chunk.getTotal() + " (min: " + webhook.minStorageCount + ", max: " + webhook.maxStorageCount + ")");
        info("- Has Shulker: " + (chunk.shulkers > 0) + " (required: " + webhook.requireShulker + ")");
        info("- Is Old Chunk: " + isOldChunk(chunk.chunkPos, mc.world.getRegistryKey()) + " (required: " + webhook.onlyOldChunks + ")");
        info("- Distance: " + String.format("%.1f", distanceFromSpawn) + " (min: " + webhook.minDistanceFromSpawn + ")");
    }

    private static String getWaypointName(Chunk chunk) {
        String waypointName = "";
        if (chunk.chests > 0) waypointName += "C:" + chunk.chests;
        if (chunk.barrels > 0) waypointName += "B:" + chunk.barrels;
        if (chunk.shulkers > 0) waypointName += "S:" + chunk.shulkers;
        if (chunk.enderChests > 0) waypointName += "E:" + chunk.enderChests;
        if (chunk.hoppers > 0) waypointName += "H:" + chunk.hoppers;
        if (chunk.dispensersDroppers > 0) waypointName += "D:" + chunk.dispensersDroppers;
        if (chunk.furnaces > 0) waypointName += "F:" + chunk.furnaces;
        return waypointName;
    }

    @Override
    public void onActivate() {
        XaeroPlus.EVENT_BUS.register(this);
        load();
    }

    @Override
    public void onDeactivate() {
        try {
            // Safely unregister from events
            XaeroPlus.EVENT_BUS.unregister(this);
            
            // Save data before deactivating
            saveJson();
            saveCsv();
            
            // Clear any pending chunk data
            chunks.clear();
        } catch (Exception e) {
            error("Error during deactivation: " + e.getMessage());
        }
    }

    @net.lenni0451.lambdaevents.EventHandler(priority = -1)
    public void onChunkData(ChunkDataEvent event) {
        if (event.seenChunk()) return;
        // Check the distance.
        double chunkXAbs = Math.abs(event.chunk().getPos().x * 16);
        double chunkZAbs = Math.abs(event.chunk().getPos().z * 16);
        if (Math.sqrt(chunkXAbs * chunkXAbs + chunkZAbs * chunkZAbs) < minimumDistance.get()) return;

        Chunk chunk = new Chunk(event.chunk().getPos());

        RegistryKey<World> currentDimension = mc.world.getRegistryKey();

        // Check that the chunk is in old chunks
        if (onlyOldchunks.get()) {
            ChunkPos chunkPos = chunk.chunkPos;
            PaletteNewChunks paletteNewChunks = ModuleManager.getModule(PaletteNewChunks.class);
            boolean is119NewChunk = paletteNewChunks
                    .isNewChunk(
                            chunkPos.x,
                            chunkPos.z,
                            currentDimension
                    );

            boolean is112OldChunk = ModuleManager.getModule(OldChunks.class)
                    .isOldChunk(
                            chunkPos.x,
                            chunkPos.z,
                            currentDimension
                    );
            if (is119NewChunk && !is112OldChunk) return;
        }

        for (BlockEntity blockEntity : event.chunk().getBlockEntities().values()) {
            if (!storageBlocks.get().contains(blockEntity.getType())) continue;

            BlockPos pos = blockEntity.getPos();
            chunk.storagePositions.add(pos);

            if (blockEntity instanceof ChestBlockEntity) chunk.chests++;
            else if (blockEntity instanceof BarrelBlockEntity) chunk.barrels++;
            else if (blockEntity instanceof ShulkerBoxBlockEntity) chunk.shulkers++;
            else if (blockEntity instanceof EnderChestBlockEntity) chunk.enderChests++;
            else if (blockEntity instanceof AbstractFurnaceBlockEntity) chunk.furnaces++;
            else if (blockEntity instanceof DispenserBlockEntity) chunk.dispensersDroppers++;
            else if (blockEntity instanceof HopperBlockEntity) chunk.hoppers++;
        }

        if ((chunk.getTotal() >= minimumStorageCount.get()) || (shulkerInstantHit.get() && chunk.shulkers > 0)) {
            Chunk prevChunk = null;
            int i = chunks.indexOf(chunk);

            if (i < 0) chunks.add(chunk);
            else prevChunk = chunks.set(i, chunk);

            saveJson();
            saveCsv();

            if (!chunk.equals(prevChunk) || !chunk.countsEqual(prevChunk)) {
                if (sendNotifications.get()) {
                    switch (notificationMode.get()) {
                        case Chat ->
                                info("Found stash at (highlight)%s(default), (highlight)%s(default).", chunk.x, chunk.z);
                        case Toast -> mc.getToastManager().add(new MeteorToast(Items.CHEST, title, "Found Stash!"));
                        case Both -> {
                            info("Found stash at (highlight)%s(default), (highlight)%s(default).", chunk.x, chunk.z);
                            mc.getToastManager().add(new MeteorToast(Items.CHEST, title, "Found Stash!"));
                        }
                    }
                }

                double distanceFromSpawn = Math.sqrt(chunk.x * chunk.x + chunk.z * chunk.z);
                
                for (WebhookConfig webhook : webhooks.get()) {
                    if (webhook.url.isEmpty()) continue;
                    
                    // Check filters
                    if (chunk.getTotal() < webhook.minStorageCount || chunk.getTotal() > webhook.maxStorageCount) continue;
                    if (webhook.requireShulker && chunk.shulkers == 0) continue;
                    if (webhook.onlyOldChunks && !isOldChunk(chunk.chunkPos, currentDimension)) continue;
                    if (distanceFromSpawn < webhook.minDistanceFromSpawn) continue;
                    
                    // Check container type filters
                    if (!webhook.includeChests && chunk.chests > 0) continue;
                    if (!webhook.includeBarrels && chunk.barrels > 0) continue;
                    if (!webhook.includeShulkers && chunk.shulkers > 0) continue;
                    if (!webhook.includeEnderChests && chunk.enderChests > 0) continue;
                    if (!webhook.includeHoppers && chunk.hoppers > 0) continue;
                    if (!webhook.includeDispensersDroppers && chunk.dispensersDroppers > 0) continue;
                    if (!webhook.includeFurnaces && chunk.furnaces > 0) continue;

                    StringBuilder message = new StringBuilder();
                    message.append("**Storage Details:**\n");
                    
                    // Get server address
                    String serverAddress = mc.getCurrentServerEntry() != null ? mc.getCurrentServerEntry().address : "singleplayer";
                    message.append("Server: ").append(serverAddress).append("\n\n");
                    
                    // Handle coordinates and dimensions
                    boolean isNether = currentDimension.getValue().getPath().equals("the_nether");
                    int overWorldX = isNether ? chunk.x * 8 : chunk.x;
                    int overWorldZ = isNether ? chunk.z * 8 : chunk.z;
                    int netherX = isNether ? chunk.x : chunk.x / 8;
                    int netherZ = isNether ? chunk.z : chunk.z / 8;
                    
                    message.append("**Coordinates:**\n");
                    message.append("Current Dimension: ").append(currentDimension.getValue().getPath()).append("\n");
                    if (isNether) {
                        message.append("Nether: ").append(chunk.x).append(", ").append(chunk.z).append("\n");
                        message.append("Overworld: ").append(overWorldX).append(", ").append(overWorldZ).append("\n");
                    } else {
                        message.append("Overworld: ").append(chunk.x).append(", ").append(chunk.z).append("\n");
                        message.append("Nether: ").append(netherX).append(", ").append(netherZ).append(" (Travel here in nether)\n");
                    }
                    
                    // Add travel directions
                    String compassDir = getDirectionsFromZeroZero(chunk.x, chunk.z);
                    message.append("\n**Travel Information:**\n");
                    message.append("Direction from 0,0: ").append(compassDir).append("\n");
                    double netherDistance = Math.sqrt(netherX * netherX + netherZ * netherZ);
                    message.append("Nether Distance: ").append(String.format("%.1f", netherDistance)).append(" blocks\n\n");
                    
                    message.append("**Container Counts:**\n");
                    
                    message.append("**Container Counts:**\n");
                    // Group storage blocks by type and collect Y levels
                    Map<String, List<Integer>> storageYLevels = new HashMap<>();
                    
                    for (BlockPos pos : chunk.storagePositions) {
                        BlockEntity be = mc.world.getBlockEntity(pos);
                        if (be == null) continue;
                        
                        String type = null;
                        if (be instanceof ChestBlockEntity && webhook.includeChests) type = "Chest";
                        else if (be instanceof BarrelBlockEntity && webhook.includeBarrels) type = "Barrel";
                        else if (be instanceof ShulkerBoxBlockEntity && webhook.includeShulkers) type = "Shulker";
                        else if (be instanceof EnderChestBlockEntity && webhook.includeEnderChests) type = "Ender Chest";
                        else if (be instanceof HopperBlockEntity && webhook.includeHoppers) type = "Hopper";
                        else if (be instanceof DispenserBlockEntity && webhook.includeDispensersDroppers) type = "Dispenser/Dropper";
                        else if (be instanceof AbstractFurnaceBlockEntity && webhook.includeFurnaces) type = "Furnace";
                        
                        if (type != null) {
                            storageYLevels.computeIfAbsent(type, k -> new ArrayList<>()).add(pos.getY());
                        }
                    }
                    
                    // Display counts with Y levels
                    for (Map.Entry<String, List<Integer>> entry : storageYLevels.entrySet()) {
                        List<Integer> yLevels = entry.getValue();
                        yLevels.sort(Integer::compareTo); // Sort Y levels
                        message.append("- ").append(entry.getKey()).append("s: ")
                              .append(yLevels.size())
                              .append(" (Y: ")
                              .append(yLevels.stream()
                                    .map(String::valueOf)
                                    .collect(Collectors.joining(", ")))
                              .append(")\n");
                    }
                    message.append("\nTotal Storage Blocks: ").append(chunk.getTotal());

                    new Thread(() -> sendWebhook(webhook.url, title, message.toString(), webhook.ping ? webhook.discordId : null, mc.player.getGameProfile().getName())).start();
                }
                if (saveToWaypoints.get()) {
                    WaypointSet waypointSet = getWaypointSet();
                    if (waypointSet == null) return;
                    addToWaypoints(waypointSet, chunk);
                    SupportMods.xaeroMinimap.requestWaypointsRefresh();
                }
            }
        }
    }

    @Override
    public WWidget getWidget(GuiTheme theme) {
        WVerticalList container = theme.verticalList();
        
        // Webhook configuration section
        WTable webhookTable = container.add(theme.table()).expandX().widget();
        webhookTable.add(theme.label("Webhook Configurations")).expandX();
        webhookTable.row();

        List<WebhookConfig> configs = webhooks.get();
        
        // Add webhook button
        WButton addWebhook = webhookTable.add(theme.button("Add Webhook")).expandX().widget();
        addWebhook.action = () -> {
                configs.add(new WebhookConfig());
                webhooks.set(configs);
                // Refresh the GUI using proper parent
                if (mc.currentScreen instanceof BetterStashFinderScreen) {
                    BetterStashFinderScreen screen = new BetterStashFinderScreen(theme);
                    mc.setScreen(screen);
                } else {
                    mc.setScreen(new BetterStashFinderScreen(theme));
                }
        };
        webhookTable.row();

        // Individual webhook configs
        for (int i = 0; i < configs.size(); i++) {
            WebhookConfig config = configs.get(i);
            int index = i;
            
            WTable configTable = webhookTable.add(theme.table()).expandX().widget();
            configTable.add(theme.label("Webhook #" + (i + 1) + " Configuration")).expandX();
            WButton delete = configTable.add(theme.button("Delete")).widget();
            delete.action = () -> {
                configs.remove(index);
                webhooks.set(configs);
                // Refresh the GUI using proper parent
                if (mc.currentScreen instanceof BetterStashFinderScreen) {
                    BetterStashFinderScreen screen = new BetterStashFinderScreen(theme);
                    mc.setScreen(screen);
                } else {
                    mc.setScreen(new BetterStashFinderScreen(theme));
                }
            };
            configTable.row();

            // Basic settings
            addTextField(theme, configTable, "Discord Webhook URL:", config.url, val -> config.url = val);
            addTextField(theme, configTable, "Discord User ID:", config.discordId, val -> config.discordId = val);
            addCheckbox(theme, configTable, "Enable Ping:", config.ping, val -> config.ping = val);

            // Filter settings
            configTable.add(theme.label("Filter Settings:")).expandX();
            configTable.row();

            addIntField(theme, configTable, "Min Storage Count:", config.minStorageCount, 0, val -> config.minStorageCount = val);
            addIntField(theme, configTable, "Max Storage Count:", config.maxStorageCount, Integer.MAX_VALUE, val -> config.maxStorageCount = val);
            addIntField(theme, configTable, "Min Distance from Spawn:", config.minDistanceFromSpawn, 0, val -> config.minDistanceFromSpawn = val);
            addCheckbox(theme, configTable, "Require Shulker:", config.requireShulker, val -> config.requireShulker = val);
            addCheckbox(theme, configTable, "Only Old Chunks:", config.onlyOldChunks, val -> config.onlyOldChunks = val);

            // Container type filters
            configTable.add(theme.label("Container Type Filters:")).expandX();
            configTable.row();

            addCheckbox(theme, configTable, "Include Chests:", config.includeChests, val -> config.includeChests = val);
            addCheckbox(theme, configTable, "Include Barrels:", config.includeBarrels, val -> config.includeBarrels = val);
            addCheckbox(theme, configTable, "Include Shulkers:", config.includeShulkers, val -> config.includeShulkers = val);
            addCheckbox(theme, configTable, "Include Ender Chests:", config.includeEnderChests, val -> config.includeEnderChests = val);
            addCheckbox(theme, configTable, "Include Hoppers:", config.includeHoppers, val -> config.includeHoppers = val);
            addCheckbox(theme, configTable, "Include Dispensers/Droppers:", config.includeDispensersDroppers, val -> config.includeDispensersDroppers = val);
            addCheckbox(theme, configTable, "Include Furnaces:", config.includeFurnaces, val -> config.includeFurnaces = val);

            webhookTable.row();
            webhookTable.add(theme.horizontalSeparator()).expandX();
            webhookTable.row();
        }

        container.add(theme.horizontalSeparator()).expandX();
        
        // Stash list section
        WVerticalList list = container.add(theme.verticalList()).widget();
        list.add(theme.label("Found Stashes")).expandX();
        
        // Sort chunks
        chunks.sort(Comparator.comparingInt(value -> -value.getTotal()));

        // Clear button
        WButton clear = list.add(theme.button("Clear")).widget();
        WTable table = new WTable();
        if (!chunks.isEmpty()) list.add(table);

        clear.action = () -> {
            removeAllStashWaypoints(chunks);
            chunks.clear();
            table.clear();
        };

        fillTable(theme, table);

        return container;
    }

    private void addTextField(GuiTheme theme, WTable table, String label, String value, java.util.function.Consumer<String> onChanged) {
        table.add(theme.label(label));
        WTextBox textBox = table.add(theme.textBox(value)).expandX().widget();
        textBox.action = () -> onChanged.accept(textBox.get());
        table.row();
    }

    private void addCheckbox(GuiTheme theme, WTable table, String label, boolean value, java.util.function.Consumer<Boolean> onChanged) {
        table.add(theme.label(label));
        WCheckbox checkbox = table.add(theme.checkbox(value)).widget();
        checkbox.action = () -> onChanged.accept(checkbox.checked);
        table.row();
    }

    private void addIntField(GuiTheme theme, WTable table, String label, int value, int defaultVal, java.util.function.Consumer<Integer> onChanged) {
        table.add(theme.label(label));
        WIntEdit intEdit = table.add(theme.intEdit(value, 0, Integer.MAX_VALUE, 0, defaultVal, true)).widget();
        intEdit.action = () -> onChanged.accept(intEdit.get() > 0 ? intEdit.get() : defaultVal);
        table.row();
    }

    private void fillTable(GuiTheme theme, WTable table) {
        for (Chunk chunk : chunks) {
            table.add(theme.label("Pos: " + chunk.x + ", " + chunk.z));
            table.add(theme.label("Total: " + chunk.getTotal()));

            WButton open = table.add(theme.button("Open")).widget();
            open.action = () -> mc.setScreen(new ChunkScreen(theme, chunk));

            WButton gotoBtn = table.add(theme.button("Goto")).widget();
            gotoBtn.action = () -> PathManagers.get().moveTo(new BlockPos(chunk.x, 0, chunk.z), true);

            WMinus delete = table.add(theme.minus()).widget();
            delete.action = () -> {
                if (chunks.remove(chunk)) {
                    table.clear();
                    fillTable(theme, table);

                    saveJson();
                    saveCsv();
                    Waypoint waypoint = getWaypointByCoordinate(chunk.x, chunk.z);
                    if (waypoint != null) {
                        WaypointSet waypointSet = getWaypointSet();
                        if (waypointSet != null) {
                            waypointSet.remove(waypoint);
                            SupportMods.xaeroMinimap.requestWaypointsRefresh();
                        }
                    }
                }
            };

            table.row();
        }
    }

    private void load() {
        boolean loaded = false;

        // Try to load json
        File file = getJsonFile();
        if (file.exists()) {
            try {
                FileReader reader = new FileReader(file);
                chunks = GSON.fromJson(reader, new TypeToken<List<Chunk>>() {
                }.getType());
                reader.close();

                for (Chunk chunk : chunks) chunk.calculatePos();

                loaded = true;
            } catch (Exception ignored) {
                if (chunks == null) chunks = new ArrayList<>();
            }
        }

        // Try to load csv
        file = getCsvFile();
        if (!loaded && file.exists()) {
            try {
                BufferedReader reader = new BufferedReader(new FileReader(file));
                reader.readLine();

                String line;
                while ((line = reader.readLine()) != null) {
                    String[] values = line.split(" ");
                    Chunk chunk = new Chunk(new ChunkPos(Integer.parseInt(values[0]), Integer.parseInt(values[1])));

                    chunk.chests = Integer.parseInt(values[2]);
                    chunk.shulkers = Integer.parseInt(values[3]);
                    chunk.enderChests = Integer.parseInt(values[4]);
                    chunk.furnaces = Integer.parseInt(values[5]);
                    chunk.dispensersDroppers = Integer.parseInt(values[6]);
                    chunk.hoppers = Integer.parseInt(values[7]);

                    chunks.add(chunk);
                }

                reader.close();
            } catch (Exception ignored) {
                if (chunks == null) chunks = new ArrayList<>();
            }
        }
        // TODO: Add all stashes as waypoints
    }

    private void saveCsv() {
        try {
            File file = getCsvFile();
            file.getParentFile().mkdirs();
            Writer writer = new FileWriter(file);

            writer.write("X,Z,Chests,Barrels,Shulkers,EnderChests,Furnaces,DispensersDroppers,Hoppers\n");
            for (Chunk chunk : chunks) chunk.write(writer);

            writer.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    private void saveJson() {
        try {
            File file = getJsonFile();
            file.getParentFile().mkdirs();
            Writer writer = new FileWriter(file);
            GSON.toJson(chunks, writer);
            writer.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    private File getJsonFile() {
        return new File(new File(new File(BetterStashFinder.FOLDER, "better-stash-finder"), Utils.getFileWorldName()), "stashes.json");
    }

    private File getCsvFile() {
        return new File(new File(new File(BetterStashFinder.FOLDER, "better-stash-finder"), Utils.getFileWorldName()), "stashes.csv");
    }

    @Override
    public String getInfoString() {
        return String.valueOf(chunks.size());
    }

    private Waypoint getWaypointByCoordinate(int x, int z) {
        WaypointSet waypointSet = getWaypointSet();
        if (waypointSet == null) return null;
        for (Waypoint waypoint : waypointSet.getWaypoints()) {
            if (waypoint.getX() == x && waypoint.getZ() == z) {
                return waypoint;
            }
        }
        return null;
    }

    private void removeAllStashWaypoints(List<Chunk> chunks) {
        WaypointSet waypointSet = getWaypointSet();
        if (waypointSet == null) return;
        for (Chunk chunk : chunks) {
            Waypoint waypoint = getWaypointByCoordinate(chunk.x, chunk.z);
            if (waypoint != null) {
                waypointSet.remove(waypoint);
            }
        }
        SupportMods.xaeroMinimap.requestWaypointsRefresh();
    }

    private WaypointSet getWaypointSet() {
        MinimapSession minimapSession = BuiltInHudModules.MINIMAP.getCurrentSession();
        if (minimapSession == null) return null;
        MinimapWorld currentWorld = minimapSession.getWorldManager().getCurrentWorld();
        if (currentWorld == null) return null;
        return currentWorld.getCurrentWaypointSet();
    }

    private void addToWaypoints(WaypointSet waypointSet, Chunk chunk) {
        int x = chunk.x;
        int z = chunk.z;

        // dont add waypoint that already exists
        if (getWaypointByCoordinate(x, z) != null) return;

        String waypointName = getWaypointName(chunk);

        // set color based on total storage blocks
        int color = 0;
        if (chunk.getTotal() < 15) color = 10; // green
        else if (chunk.getTotal() < 50) color = 14; // i forgot what these are lmao
        else if (chunk.getTotal() < 100) color = 12;
        else if (chunk.getTotal() >= 100) color = 4; // red i think

        Waypoint waypoint = new Waypoint(
                x,
                70,
                z,
                waypointName,
                "S",
                color,
                0,
                false);

        waypointSet.add(waypoint);
    }

    private void waypointSettingChanged(boolean enabled) {
        if (!enabled) {
            removeAllStashWaypoints(chunks);
        } else {
            WaypointSet waypointSet = getWaypointSet();
            if (waypointSet == null) return;
            for (Chunk chunk : chunks) {
                addToWaypoints(waypointSet, chunk);
            }
            SupportMods.xaeroMinimap.requestWaypointsRefresh();
        }
    }


    public enum Mode {
        Chat,
        Toast,
        Both
    }

    public static class Chunk {
        private static final StringBuilder sb = new StringBuilder();

        public ChunkPos chunkPos;
        public transient int x, z;
        public transient List<BlockPos> storagePositions;
        public int chests, barrels, shulkers, enderChests, furnaces, dispensersDroppers, hoppers;

        public Chunk(ChunkPos chunkPos) {
            this.chunkPos = chunkPos;
            this.storagePositions = new ArrayList<>();
            calculatePos();
        }

        public void calculatePos() {
            x = chunkPos.x * 16 + 8;
            z = chunkPos.z * 16 + 8;
        }

        public int getTotal() {
            return chests + barrels + shulkers + enderChests + furnaces + dispensersDroppers + hoppers;
        }

        public void write(Writer writer) throws IOException {
            sb.setLength(0);
            sb.append(x).append(',').append(z).append(',');
            sb.append(chests).append(',').append(barrels).append(',').append(shulkers).append(',').append(enderChests).append(',').append(furnaces).append(',').append(dispensersDroppers).append(',').append(hoppers).append('\n');
            writer.write(sb.toString());
        }

        public boolean countsEqual(Chunk c) {
            if (c == null) return false;
            return chests != c.chests || barrels != c.barrels || shulkers != c.shulkers || enderChests != c.enderChests || furnaces != c.furnaces || dispensersDroppers != c.dispensersDroppers || hoppers != c.hoppers;
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            Chunk chunk = (Chunk) o;
            return Objects.equals(chunkPos, chunk.chunkPos);
        }

        @Override
        public int hashCode() {
            return Objects.hash(chunkPos);
        }
    }

    private class BetterStashFinderScreen extends WindowScreen {
        private final BetterStashFinder parent;

        public BetterStashFinderScreen(GuiTheme theme) {
            super(theme, "Stash Finder Settings");
            this.parent = BetterStashFinder.this;
        }

        @Override
        public void initWidgets() {
            add(parent.getWidget(theme)).expandX();
        }
    }

    private static class ChunkScreen extends WindowScreen {
        private final Chunk chunk;

        public ChunkScreen(GuiTheme theme, Chunk chunk) {
            super(theme, "Chunk at " + chunk.x + ", " + chunk.z);

            this.chunk = chunk;
        }

        @Override
        public void initWidgets() {
            WTable t = add(theme.table()).expandX().widget();

            // Total
            t.add(theme.label("Total:"));
            t.add(theme.label(chunk.getTotal() + ""));
            t.row();

            t.add(theme.horizontalSeparator()).expandX();
            t.row();

            // Separate
            t.add(theme.label("Chests:"));
            t.add(theme.label(chunk.chests + ""));
            t.row();

            t.add(theme.label("Barrels:"));
            t.add(theme.label(chunk.barrels + ""));
            t.row();

            t.add(theme.label("Shulkers:"));
            t.add(theme.label(chunk.shulkers + ""));
            t.row();

            t.add(theme.label("Ender Chests:"));
            t.add(theme.label(chunk.enderChests + ""));
            t.row();

            t.add(theme.label("Furnaces:"));
            t.add(theme.label(chunk.furnaces + ""));
            t.row();

            t.add(theme.label("Dispensers and droppers:"));
            t.add(theme.label(chunk.dispensersDroppers + ""));
            t.row();

            t.add(theme.label("Hoppers:"));
            t.add(theme.label(chunk.hoppers + ""));
        }
    }
    
    private String getDirectionsFromZeroZero(int x, int z) {
        StringBuilder directions = new StringBuilder();
        
        if (z < 0) {
            directions.append("North ");
        } else if (z > 0) {
            directions.append("South ");
        }
        
        if (x < 0) {
            directions.append("West");
        } else if (x > 0) {
            directions.append("East");
        }
        
        if (directions.length() == 0) {
            return "At 0,0";
        }
        
        return directions.toString().trim();
    }
}