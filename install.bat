@echo off
set "MOD_PATH=build\libs\Skylandia-1.2-1.21.4.jar"
set "MODS_DIR=C:\Users\<USER>\Desktop\mmc-develop-win32\MultiMC\instances\bot 2\.minecraft\mods"

echo Installing mod...

if not exist "%MOD_PATH%" (
    echo Error: Mod file not found at %MOD_PATH%
    pause
    exit /b 1
)

if exist "%MODS_DIR%\Skylandia-*.jar" (
    echo Removing old version...
    del /f "%MODS_DIR%\Skylandia-*.jar"
)

echo Copying new version...
copy /Y "%MOD_PATH%" "%MODS_DIR%\"

if %ERRORLEVEL% EQU 0 (
    echo Installation successful!
    echo Installed to: %MODS_DIR%
) else (
    echo Failed to install mod!
    pause
    exit /b 1
)