package dev.journey.PathSeeker.modules.utility;

import dev.journey.PathSeeker.PathSeeker;
import meteordevelopment.meteorclient.settings.IntSetting;
import meteordevelopment.meteorclient.events.world.TickEvent;
import meteordevelopment.orbit.EventHandler;
import meteordevelopment.meteorclient.settings.Setting;
import meteordevelopment.meteorclient.settings.SettingGroup;
import meteordevelopment.meteorclient.systems.modules.Module;
import net.minecraft.entity.EquipmentSlot;
import net.minecraft.item.Item;
import net.minecraft.item.ItemStack;
import net.minecraft.item.Items;

public class ElytraSwap extends Module {
    private final SettingGroup sgWarning = settings.createGroup("Warning");

    private final Setting<Integer> durabilityThreshold = sgWarning.add(new IntSetting.Builder()
            .name("durability-threshold")
            .description("Durability threshold to trigger warning.")
            .defaultValue(20)
            .min(1)
            .sliderRange(1, 432)
            .build()
    );

    private final Setting<Integer> checkInterval = sgWarning.add(new IntSetting.Builder()
            .name("check-interval")
            .description("How often to check durability in ticks (20 ticks = 1 second).")
            .defaultValue(20)
            .min(1)
            .sliderRange(1, 200)
            .build()
    );

    private int tickCounter = 0;

    @Override
    public void onActivate() {
        tickCounter = 0;
    }

    @EventHandler
    private void onTick(TickEvent.Post event) {
        tickCounter++;
        if (tickCounter >= checkInterval.get()) {
            tickCounter = 0;
            
            ItemStack chest = mc.player.getEquippedStack(EquipmentSlot.CHEST);
            if (chest.getItem() == Items.ELYTRA) {
                int maxDurability = chest.getMaxDamage();
                int damage = chest.getDamage();
                int durability = maxDurability - damage;
                if (durability <= durabilityThreshold.get()) {
                    warning("Elytra durability low! (%d durability remaining)", durability);
                }
            }
        }
    }

    public ElytraSwap() {
        super(PathSeeker.Utility, "Skylandia", "Warns you when your elytra durability is getting low.");
    }
}
