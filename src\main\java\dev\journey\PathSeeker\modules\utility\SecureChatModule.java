package dev.journey.PathSeeker.modules.utility;

import dev.journey.PathSeeker.PathSeeker;
import dev.journey.PathSeeker.utils.SecureChat;
import meteordevelopment.meteorclient.events.game.ReceiveMessageEvent;
import meteordevelopment.meteorclient.events.game.SendMessageEvent;
import meteordevelopment.meteorclient.settings.*;
import meteordevelopment.meteorclient.systems.modules.Module;
import meteordevelopment.orbit.EventHandler;
import net.minecraft.text.*;
import net.minecraft.util.Formatting;

import java.security.GeneralSecurityException;

public class SecureChatModule extends Module {
    private final SettingGroup sgGeneral = settings.getDefaultGroup();

    // Settings
    private final Setting<String> password = sgGeneral.add(new StringSetting.Builder()
        .name("password")
        .description("The password used for encryption/decryption")
        .defaultValue("")
        .build()
    );

    private final Setting<String> prefix = sgGeneral.add(new StringSetting.Builder()
        .name("message-prefix")
        .description("The prefix to identify encrypted messages")
        .defaultValue("[[SECURE]]")
        .build()
    );

    private final Setting<Boolean> hidePassword = sgGeneral.add(new BoolSetting.Builder()
        .name("hide-password")
        .description("Hide the password in the GUI")
        .defaultValue(true)
        .build()
    );

    public SecureChatModule() {
        super(PathSeeker.Utility, "secure-chat", "Enables encrypted party chat using AES-256");
    }

    @EventHandler
    private void onMessageSend(SendMessageEvent event) {
        String msg = event.message;
        if (msg.startsWith("/")) return; // Don't encrypt commands

        try {
            // Encrypt the message
            String encrypted = SecureChat.encrypt(msg, password.get());
            
            // Format with prefix and send
            event.message = prefix.get() + encrypted;
        } catch (GeneralSecurityException e) {
            error("Failed to encrypt message: " + e.getMessage());
            event.cancel();
        }
    }

    @EventHandler
    private void onMessageReceive(ReceiveMessageEvent event) {
        String msg = event.getMessage().getString();
        if (!msg.startsWith(prefix.get())) return;

        try {
            // Extract encrypted part
            String encrypted = msg.substring(prefix.get().length());
            
            // Decrypt
            String decrypted = SecureChat.decrypt(encrypted, password.get());
            
            // Replace message with decrypted version
            event.cancel();
            info("§a[Decrypted]§r " + decrypted);
        } catch (GeneralSecurityException e) {
            // Silently ignore decryption failures as they might be messages encrypted with different passwords
        }
    }

    @Override
    public void onActivate() {
        if (password.get().isEmpty()) {
            error("Password not set! Please set a password in the module settings.");
            toggle();
        }
    }
}